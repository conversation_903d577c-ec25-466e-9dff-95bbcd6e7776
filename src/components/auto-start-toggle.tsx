'use client';

import { useSettings } from '@/providers/settings-provider';
import { useLanguage } from '@/providers/language-provider';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import { cn } from '@/lib/utils';

export function AutoStartToggle() {
  const { settings, setSettings } = useSettings();
  const { t } = useLanguage();

  const handleToggle = (checked: boolean) => {
    setSettings({ autoStartNextPeriod: checked });
  };

  return (
    <div className="flex items-center space-x-3 p-3 md:p-4 rounded-lg bg-card/60 backdrop-blur-sm border border-border/40 shadow-sm hover:bg-card/70 transition-colors">
      <Switch
        id="auto-start"
        checked={settings.autoStartNextPeriod}
        onCheckedChange={handleToggle}
        className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
      />
      <Label
        htmlFor="auto-start"
        className={cn(
          "text-sm md:text-base font-medium cursor-pointer select-none",
          "text-foreground/90 hover:text-foreground transition-colors"
        )}
      >
        {t('controls.auto_start')}
      </Label>
    </div>
  );
}
